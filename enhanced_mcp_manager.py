"""
Enhanced MCP Manager with Context Retention
Extends the existing MCP functionality with session-aware conversation management
"""

import os
import logging
from typing import Dict, List, Any, Optional
import asyncio
from session_manager import session_manager, ChatSession

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add context-aware functionality to existing MCPClientManager
    This can be mixed into your existing MCPClientManager without breaking changes
    """
    
    async def chat_with_bedrock_with_context(self, message: str, session_id: str, 
                                           tools_available: List[str] = None) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with session context retention
        
        Args:
            message: User's message
            session_id: Session identifier for context retention
            tools_available: List of available tool keys
            
        Returns:
            Dict containing response, tools_used, and session_id
        """
        try:
            # Get or create session for context
            session = session_manager.get_or_create_session(session_id)
            
            # Get conversation history for context (limit to prevent token overflow)
            historical_messages = session.get_bedrock_messages(max_turns=8)
            
            # Build current messages with history
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            # Enhanced system message with session context
            system_message = self._build_context_aware_system_message(session, tools_available)
            
            # Prepare tool configuration
            tool_config = self._build_tool_config_for_session(tools_available)
            
            # Execute conversation with context
            result = await self._execute_contextual_conversation(
                current_messages, system_message, tool_config, session_id
            )
            
            # Store conversation turn in session
            session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result["tools_used"]
            )
            
            logger.info(f"Completed contextual chat for session {session_id}: "
                       f"{len(result['tools_used'])} tools used")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in contextual chat for session {session_id}: {e}")
            return {
                "response": f"I apologize, but I encountered an error: {str(e)}",
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }
    
    def _build_context_aware_system_message(self, session: ChatSession, 
                                          tools_available: List[str] = None) -> str:
        """Build enhanced system message with session context"""
        
        # Base system message
        base_message = ("You are a helpful AI assistant with access to AWS tools. "
                       "You have conversation context and can reference previous interactions.")
        
        # Add session context
        session_context = session.get_context_for_bedrock()
        if session_context:
            base_message += f"\n\n=== SESSION CONTEXT ===\n{session_context}\n"
            base_message += ("Use this context to provide more relevant responses. "
                           "You can reference previous questions, tool results, and ongoing topics.")
        
        # Add available tools information
        if tools_available:
            available_tools = self.get_available_tools()
            tools_info = "\n\n=== AVAILABLE TOOLS ===\n"
            
            for tool_key in tools_available:
                if tool_key in available_tools:
                    tool_data = available_tools[tool_key]
                    tool = tool_data["tool"]
                    tools_info += f"- {tool['name']} (server: {tool_data['server']}): {tool['description']}\n"
            
            base_message += tools_info
        
        # Add context-aware instructions
        base_message += ("\n\n=== CONTEXT INSTRUCTIONS ===\n"
                        "- Reference previous conversations when relevant\n"
                        "- Build upon previous tool results when applicable\n"
                        "- Maintain conversation continuity\n"
                        "- If user asks follow-up questions, use session context to understand what they're referring to")
        
        return base_message
    
    def _build_tool_config_for_session(self, tools_available: List[str] = None) -> Optional[Dict]:
        """Build tool configuration with session awareness"""
        if not tools_available:
            return None
        
        available_tools = self.get_available_tools()
        tools = []
        
        for tool_key in tools_available:
            if tool_key in available_tools:
                tool_data = available_tools[tool_key]
                tool = tool_data["tool"]
                
                # Ensure proper schema format
                input_schema = tool.get("input_schema", {})
                if not input_schema:
                    input_schema = {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                
                # Ensure required structure
                if "type" not in input_schema:
                    input_schema["type"] = "object"
                if "properties" not in input_schema:
                    input_schema["properties"] = {}
                
                tools.append({
                    "toolSpec": {
                        "name": tool["name"],
                        "description": tool["description"],
                        "inputSchema": {
                            "json": input_schema
                        }
                    }
                })
        
        if tools:
            return {
                "tools": tools,
                "toolChoice": {"auto": {}}
            }
        
        return None
    
    async def _execute_contextual_conversation(self, messages: List[Dict], system_message: str,
                                             tool_config: Optional[Dict], session_id: str) -> Dict[str, Any]:
        """Execute conversation with context and tool support"""
        
        tools_used = []
        max_iterations = 10
        iteration = 0
        
        while iteration < max_iterations:
            iteration += 1
            logger.info(f"Contextual conversation iteration {iteration} for session {session_id}")
            
            # Manage context window size
            messages = self._manage_context_window(messages, max_tokens=80000)
            
            converse_params = {
                "modelId": os.getenv('BEDROCK_MODEL_ID'),
                "messages": messages,
                "system": [{"text": system_message}],
                "inferenceConfig": {
                    "maxTokens": 4000,
                    "temperature": 0.7,
                    "topP": 0.9
                }
            }
            
            if tool_config:
                converse_params["toolConfig"] = tool_config
            
            response = self.bedrock_client.converse(**converse_params)
            stop_reason = response.get('stopReason')
            
            logger.info(f"Bedrock response stop reason: {stop_reason}")
            
            if stop_reason == 'tool_use':
                assistant_message = response.get('output', {}).get('message', {})
                messages.append(assistant_message)
                
                turn_tool_results = []
                
                for content in assistant_message.get('content', []):
                    if content.get('toolUse'):
                        tool_use = content['toolUse']
                        tool_name = tool_use.get('name')
                        tool_input = tool_use.get('input', {})
                        tool_use_id = tool_use.get('toolUseId')
                        
                        logger.info(f"Executing tool: {tool_name} with input: {tool_input}")
                        
                        # Find server for this tool
                        server_name = self._find_server_for_tool(tool_name)
                        
                        if server_name:
                            result = await self.call_tool(server_name, tool_name, tool_input)
                            
                            # Track tool usage with context
                            tool_usage = {
                                "tool_name": tool_name,
                                "server_name": server_name,
                                "input": tool_input,
                                "success": result["success"],
                                "session_id": session_id,
                                "iteration": iteration
                            }
                            
                            # Store truncated result for context (avoid memory bloat)
                            if result["success"]:
                                result_str = str(result.get("result", ""))
                                tool_usage["result"] = result_str[:500] + "..." if len(result_str) > 500 else result_str
                            else:
                                tool_usage["error"] = result.get("error", "Unknown error")
                            
                            tools_used.append(tool_usage)
                            
                            # Format result for Bedrock
                            result_content = result.get("result", str(result)) if result["success"] else f"Error: {result.get('error', 'Unknown error')}"
                            
                            turn_tool_results.append({
                                "toolUseId": tool_use_id,
                                "content": [{"text": str(result_content)}],
                                "status": "success" if result["success"] else "error"
                            })
                        else:
                            logger.error(f"Server not found for tool: {tool_name}")
                            turn_tool_results.append({
                                "toolUseId": tool_use_id,
                                "content": [{"text": f"Error: Tool {tool_name} not found"}],
                                "status": "error"
                            })
                
                # Add tool results to conversation
                if turn_tool_results:
                    tool_result_message = {
                        "role": "user",
                        "content": []
                    }
                    
                    for tool_result in turn_tool_results:
                        tool_result_message["content"].append({
                            "toolResult": {
                                "toolUseId": tool_result["toolUseId"],
                                "content": tool_result["content"],
                                "status": tool_result["status"]
                            }
                        })
                    
                    messages.append(tool_result_message)
                
                continue
                
            elif stop_reason in ['end_turn', 'stop_sequence', 'max_tokens']:
                output_message = response.get('output', {}).get('message', {})
                final_response = ""
                
                for content in output_message.get('content', []):
                    if content.get('text'):
                        final_response = content['text']
                        break
                
                return {
                    "response": final_response or "Response completed.",
                    "tools_used": tools_used,
                    "session_id": session_id
                }
            
            else:
                logger.warning(f"Unexpected stop reason: {stop_reason}")
                output_message = response.get('output', {}).get('message', {})
                for content in output_message.get('content', []):
                    if content.get('text'):
                        return {
                            "response": content['text'],
                            "tools_used": tools_used,
                            "session_id": session_id
                        }
                
                return {
                    "response": f"Response completed with stop reason: {stop_reason}",
                    "tools_used": tools_used,
                    "session_id": session_id
                }
        
        return {
            "response": "Response completed after maximum iterations.",
            "tools_used": tools_used,
            "session_id": session_id
        }
    
    def _find_server_for_tool(self, tool_name: str) -> Optional[str]:
        """Find which server provides a specific tool"""
        available_tools = self.get_available_tools()
        
        for tool_key, tool_data in available_tools.items():
            if tool_data["tool"]["name"] == tool_name:
                return tool_data["server"]
        
        return None
    
    def _manage_context_window(self, messages: List[Dict], max_tokens: int = 80000) -> List[Dict]:
        """Manage context window size by truncating old messages if needed"""
        # Rough token estimation (4 characters ≈ 1 token)
        total_chars = sum(len(str(msg)) for msg in messages)
        estimated_tokens = total_chars // 4
        
        if estimated_tokens > max_tokens:
            logger.info(f"Context window management: {estimated_tokens} tokens, truncating...")
            
            # Keep recent messages, remove oldest
            while len(messages) > 4 and estimated_tokens > max_tokens:
                removed_msg = messages.pop(0)
                total_chars -= len(str(removed_msg))
                estimated_tokens = total_chars // 4
            
            logger.info(f"Truncated to {len(messages)} messages, ~{estimated_tokens} tokens")
        
        return messages
