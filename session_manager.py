"""
Session Management for MCP Bot with Context Retention
Handles conversation history, context tracking, and session persistence
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class ConversationTurn:
    """Represents a single conversation turn with context"""
    timestamp: datetime
    user_message: str
    assistant_response: str
    tools_used: List[Dict[str, Any]]
    session_id: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_message": self.user_message,
            "assistant_response": self.assistant_response,
            "tools_used": self.tools_used,
            "session_id": self.session_id
        }

class ChatSession:
    """Manages individual chat session with conversation history and context"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.conversation_history: List[ConversationTurn] = []
        self.context_summary = ""
        self.total_tools_used = 0
    
    def add_turn(self, user_message: str, assistant_response: str, tools_used: List[Dict] = None):
        """Add a conversation turn with context tracking"""
        tools_used = tools_used or []
        
        turn = ConversationTurn(
            timestamp=datetime.now(),
            user_message=user_message,
            assistant_response=assistant_response,
            tools_used=tools_used,
            session_id=self.session_id
        )
        
        self.conversation_history.append(turn)
        self.last_activity = datetime.now()
        self.total_tools_used += len(tools_used)
        
        # Update context summary
        self._update_context_summary()
        
        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")
    
    def get_bedrock_messages(self, max_turns: int = 10) -> List[Dict]:
        """Convert conversation history to Bedrock message format with context"""
        messages = []
        
        # Get recent conversation turns (limit to prevent token overflow)
        recent_turns = self.conversation_history[-max_turns:]
        
        for turn in recent_turns:
            # Add user message
            messages.append({
                "role": "user",
                "content": [{"text": turn.user_message}]
            })
            
            # Add assistant response
            messages.append({
                "role": "assistant",
                "content": [{"text": turn.assistant_response}]
            })
        
        logger.info(f"Retrieved {len(messages)} messages for session {self.session_id}")
        return messages
    
    def get_context_for_bedrock(self) -> str:
        """Generate context summary for system message"""
        context_parts = []
        
        if self.conversation_history:
            context_parts.append(f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}")
            context_parts.append(f"Total conversation turns: {len(self.conversation_history)}")
            context_parts.append(f"Total tools executed: {self.total_tools_used}")
            
            if self.context_summary:
                context_parts.append(f"Recent context: {self.context_summary}")
            
            # Add recent tool usage context
            recent_tools = []
            recent_servers = set()
            
            for turn in self.conversation_history[-3:]:  # Last 3 turns
                for tool in turn.tools_used:
                    if tool.get("success"):
                        tool_name = tool.get('tool_name', 'unknown')
                        server_name = tool.get('server_name', 'unknown')
                        recent_tools.append(tool_name)
                        recent_servers.add(server_name)
            
            if recent_tools:
                context_parts.append(f"Recently used tools: {', '.join(set(recent_tools))}")
                context_parts.append(f"Active servers: {', '.join(recent_servers)}")
        
        return "\n".join(context_parts)
    
    def _update_context_summary(self):
        """Update context summary based on recent conversation"""
        if len(self.conversation_history) >= 2:
            # Analyze recent topics and tool usage
            recent_topics = []
            recent_tool_types = []
            
            for turn in self.conversation_history[-3:]:  # Last 3 turns
                # Extract key topics from user messages
                user_msg = turn.user_message.lower()
                if any(word in user_msg for word in ['budget', 'cost', 'billing']):
                    recent_topics.append('cost_analysis')
                if any(word in user_msg for word in ['cloudformation', 'stack', 'template']):
                    recent_topics.append('infrastructure')
                if any(word in user_msg for word in ['pricing', 'price', 'compare']):
                    recent_topics.append('pricing_analysis')
                
                # Track tool categories
                for tool in turn.tools_used:
                    server = tool.get('server_name', '')
                    if 'cost-explorer' in server:
                        recent_tool_types.append('cost_analysis')
                    elif 'billing' in server:
                        recent_tool_types.append('billing_management')
                    elif 'cloudformation' in server:
                        recent_tool_types.append('infrastructure_management')
                    elif 'pricing' in server:
                        recent_tool_types.append('pricing_analysis')
            
            # Build context summary
            summary_parts = []
            if recent_topics:
                summary_parts.append(f"Topics: {', '.join(set(recent_topics))}")
            if recent_tool_types:
                summary_parts.append(f"Tool categories: {', '.join(set(recent_tool_types))}")
            
            self.context_summary = "; ".join(summary_parts)
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        successful_tools = 0
        failed_tools = 0
        servers_used = set()
        
        for turn in self.conversation_history:
            for tool in turn.tools_used:
                if tool.get("success"):
                    successful_tools += 1
                else:
                    failed_tools += 1
                
                server_name = tool.get("server_name")
                if server_name:
                    servers_used.add(server_name)
        
        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "successful_tools": successful_tools,
            "failed_tools": failed_tools,
            "servers_used": list(servers_used),
            "context_summary": self.context_summary
        }

class SessionManager:
    """Manages multiple chat sessions with automatic cleanup"""
    
    def __init__(self, session_timeout_hours: int = 2):
        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=session_timeout_hours)
        logger.info(f"SessionManager initialized with {session_timeout_hours}h timeout")
    
    def get_or_create_session(self, session_id: str) -> ChatSession:
        """Get existing session or create new one"""
        if session_id not in self.sessions:
            self.sessions[session_id] = ChatSession(session_id)
            logger.info(f"Created new session: {session_id}")
        else:
            logger.info(f"Retrieved existing session: {session_id}")
        
        session = self.sessions[session_id]
        session.last_activity = datetime.now()
        return session
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get session if it exists"""
        return self.sessions.get(session_id)
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a specific session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Deleted session: {session_id}")
            return True
        return False
    
    def cleanup_expired_sessions(self) -> int:
        """Remove expired sessions and return count of cleaned up sessions"""
        now = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        
        return len(expired_sessions)
    
    def get_all_sessions_stats(self) -> Dict[str, Any]:
        """Get statistics for all sessions"""
        total_sessions = len(self.sessions)
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())
        
        active_sessions = 0
        now = datetime.now()
        for session in self.sessions.values():
            if now - session.last_activity < timedelta(minutes=30):  # Active in last 30 min
                active_sessions += 1
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_conversation_turns": total_turns,
            "total_tools_executed": total_tools,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600
        }

# Global session manager instance
session_manager = SessionManager()
