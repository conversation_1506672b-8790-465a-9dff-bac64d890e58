#!/usr/bin/env python3
"""
Quick Start Script for Enhanced MCP Bot with Context Retention
Launches both the enhanced backend API and Streamlit frontend
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path
import requests
import argparse

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi', 'uvicorn', 'streamlit', 'boto3', 'mcp',
        'plotly', 'pandas', 'dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies are installed")
    return True

def check_env_file():
    """Check if .env file exists and has required variables"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("⚠️  .env file not found. Creating template...")
        
        env_template = """# Enhanced MCP Bot Configuration
# AWS Configuration
AWS_PROFILE=default
AWS_REGION=ap-south-1

# Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20241022-v2:0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_TIMEOUT=120
LOG_LEVEL=info

# Session Configuration
AUTO_CONFIGURE_SERVERS=true
SESSION_TIMEOUT_HOURS=2

# Streamlit Configuration
STREAMLIT_PORT=8501
"""
        
        with open('.env', 'w') as f:
            f.write(env_template)
        
        print("✅ Created .env template file")
        print("Please review and update the configuration as needed")
    
    return True

def wait_for_api(host="localhost", port=8000, timeout=60):
    """Wait for the API to become available"""
    print(f"⏳ Waiting for Enhanced API at http://{host}:{port}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"http://{host}:{port}/", timeout=5)
            if response.status_code == 200:
                print("✅ Enhanced API is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(2)
    
    print(f"❌ API did not start within {timeout} seconds")
    return False

def start_api_server(host="0.0.0.0", port=8000):
    """Start the enhanced FastAPI server"""
    print(f"🚀 Starting Enhanced MCP API server on {host}:{port}...")
    
    cmd = [
        sys.executable, "-m", "uvicorn",
        "main_enhanced:app",
        "--host", host,
        "--port", str(port),
        "--reload",
        "--log-level", "info"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print API startup logs in a separate thread
        def print_api_logs():
            for line in process.stdout:
                print(f"[API] {line.strip()}")
        
        log_thread = threading.Thread(target=print_api_logs, daemon=True)
        log_thread.start()
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start API server: {e}")
        return None

def start_streamlit_app(port=8501):
    """Start the enhanced Streamlit frontend"""
    print(f"🎨 Starting Enhanced Streamlit app on port {port}...")
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "app_enhanced.py",
        "--server.port", str(port),
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false"
    ]
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print Streamlit startup logs in a separate thread
        def print_streamlit_logs():
            for line in process.stdout:
                if "You can now view your Streamlit app" in line:
                    print(f"✅ Enhanced Streamlit app is ready!")
                print(f"[Streamlit] {line.strip()}")
        
        log_thread = threading.Thread(target=print_streamlit_logs, daemon=True)
        log_thread.start()
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start Streamlit app: {e}")
        return None

def cleanup_processes(processes):
    """Clean up running processes"""
    print("\n🧹 Cleaning up processes...")
    
    for name, process in processes.items():
        if process and process.poll() is None:
            print(f"Stopping {name}...")
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"Force killing {name}...")
                process.kill()
                process.wait()
    
    print("✅ Cleanup complete")

def main():
    parser = argparse.ArgumentParser(description="Start Enhanced MCP Bot with Context Retention")
    parser.add_argument("--api-port", type=int, default=8000, help="API server port")
    parser.add_argument("--streamlit-port", type=int, default=8501, help="Streamlit app port")
    parser.add_argument("--api-only", action="store_true", help="Start only the API server")
    parser.add_argument("--streamlit-only", action="store_true", help="Start only the Streamlit app")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser automatically")
    
    args = parser.parse_args()
    
    print("🧠 Enhanced MCP Bot with Context Retention")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check environment file
    check_env_file()
    
    processes = {}
    
    try:
        # Start API server (unless streamlit-only)
        if not args.streamlit_only:
            api_process = start_api_server(port=args.api_port)
            if not api_process:
                sys.exit(1)
            
            processes['API'] = api_process
            
            # Wait for API to be ready
            if not wait_for_api(port=args.api_port):
                cleanup_processes(processes)
                sys.exit(1)
        
        # Start Streamlit app (unless api-only)
        if not args.api_only:
            # Set environment variable for API URL
            os.environ['API_BASE_URL'] = f"http://localhost:{args.api_port}"
            
            streamlit_process = start_streamlit_app(port=args.streamlit_port)
            if not streamlit_process:
                cleanup_processes(processes)
                sys.exit(1)
            
            processes['Streamlit'] = streamlit_process
            
            # Wait a bit for Streamlit to start
            time.sleep(3)
            
            # Open browser (unless disabled)
            if not args.no_browser:
                import webbrowser
                streamlit_url = f"http://localhost:{args.streamlit_port}"
                print(f"🌐 Opening browser to {streamlit_url}")
                webbrowser.open(streamlit_url)
        
        # Print status
        print("\n" + "=" * 50)
        print("🎉 Enhanced MCP Bot is running!")
        print("=" * 50)
        
        if 'API' in processes:
            print(f"📡 Enhanced API: http://localhost:{args.api_port}")
            print(f"📚 API Docs: http://localhost:{args.api_port}/docs")
        
        if 'Streamlit' in processes:
            print(f"🎨 Enhanced UI: http://localhost:{args.streamlit_port}")
        
        print("\n🧠 Features:")
        print("  • Context retention across conversations")
        print("  • Session management and history")
        print("  • Enhanced tool tracking")
        print("  • Multi-server MCP integration")
        print("  • Real-time AWS cost analysis")
        
        print("\n⌨️  Press Ctrl+C to stop all services")
        print("=" * 50)
        
        # Keep running until interrupted
        try:
            while True:
                # Check if processes are still running
                for name, process in list(processes.items()):
                    if process.poll() is not None:
                        print(f"⚠️  {name} process has stopped")
                        del processes[name]
                
                if not processes:
                    print("❌ All processes have stopped")
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested...")
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    finally:
        cleanup_processes(processes)

if __name__ == "__main__":
    main()
